package com.wexl.gilico.service;

import com.wexl.dps.reportcard.BaseReportCardDefinition;
import com.wexl.gilico.dto.GilcoHolisticReportModeldto;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.model.CompetenciesStudents;
import com.wexl.holisticreportcards.model.CompetencyTypes;
import com.wexl.holisticreportcards.model.ProgressCard;
import com.wexl.holisticreportcards.repository.CompetenciesStudentRepository;
import com.wexl.holisticreportcards.repository.FacilitatorStudentsRepository;
import com.wexl.holisticreportcards.repository.ProgressCardRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.profile.ProfileService;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class GillcoHolisticReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final CompetenciesStudentRepository competenciesStudentRepository;
  private final ProfileService profileService;
  private final ProgressCardRepository progressCardRepository;
  private final ProgressCardService progressCardService;
  private final FacilitatorStudentsRepository facilitatorStudentsRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHolisticReportCardHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  private GilcoHolisticReportModeldto.Body buildBody(User user, String slug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var mother =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findFirst();
    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst();
    var motherName = mother == null ? null : reportCardService.getGuardianName(mother.get());
    var fatherName = father == null ? null : reportCardService.getGuardianName(father.get());
    var studentData = progressCardRepository.findByStudentId(student.getId());
    Optional<StudentAttributeValueModel> dateOfBirthOpt =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var dateOfBirth = dateOfBirthOpt.map(StudentAttributeValueModel::getValue).orElse(null);
    var age = getAge(dateOfBirth);
    Optional<StudentAttributeValueModel> house =
        reportCardService.getStudentAttributeValue(student, "house");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residential_address");

    return GilcoHolisticReportModeldto.Body.builder()
        .name(user.getFirstName() + " " + user.getLastName())
        .className(student.getSection().getGradeName())
        .rollNo(student.getClassRollNumber())
        .sectionName(student.getSection().getName())
        .admissionNumber(student.getRollNumber())
        .house(house.map(StudentAttributeValueModel::getValue).orElse(null))
        .dateOfBirth(dateOfBirth)
        .age(age)
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(fatherName)
        .motherName(motherName)
        .allAboutMe(buildAllAboutMe(studentData))
        .term1(buildTerm1(student, slug))
        .term2(buildTerm2(student, slug))
        .build();
  }

  public Integer getAge(String dateOfBirth) {
    if (dateOfBirth == null || dateOfBirth.isEmpty()) {
      return null;
    }
    List<DateTimeFormatter> formatters =
        List.of(
            DateTimeFormatter.ofPattern("dd/MM/yyyy"), DateTimeFormatter.ofPattern("yyyy/MM/dd"));

    for (DateTimeFormatter formatter : formatters) {
      try {
        LocalDate dob = LocalDate.parse(dateOfBirth, formatter);
        return Period.between(dob, LocalDate.now()).getYears();
      } catch (DateTimeParseException ignored) {
        log.info("Invalid date format for dateOfBirth: " + dateOfBirth);
      }
    }
    return null;
  }

  public GilcoHolisticReportModeldto.AllAboutMe buildAllAboutMe(ProgressCard studentData) {

    if (Objects.isNull(studentData)) {
      return null;
    }

    return GilcoHolisticReportModeldto.AllAboutMe.builder()
        .familyPhoto(
            Objects.isNull(studentData)
                ? null
                : Objects.isNull(studentData.getAGlimpseOfMyFamily())
                    ? null
                    : progressCardService.fetchImage(studentData.getAGlimpseOfMyFamily()))
        .studentPhoto(
            Objects.isNull(studentData)
                ? null
                : Objects.isNull(studentData.getAGlimpseOfMySelf())
                    ? null
                    : progressCardService.fetchImage(studentData.getAGlimpseOfMySelf()))
        .liveIn(studentData.getILiveIn())
        .favAnimals(studentData.getMyFavouriteAnimals())
        .favColours(studentData.getMyFavouriteColoursAre())
        .favFoods(studentData.getMyFavouriteFoods())
        .favGames(studentData.getMyFavouriteGames())
        .friends(studentData.getMyFriendsAre())
        .term1Height(studentData.getTerm1Height())
        .term1Weight(studentData.getTerm1Weight())
        .term2Height(studentData.getTerm2Height())
        .term2Weight(studentData.getTerm2Weight())
        .things(studentData.getThingsILike())
        .aim(studentData.getAim())
        .favFlower(studentData.getFlower())
        .favSports(studentData.getMyFavouriteGames())
        .favSubject(studentData.getSubject())
        .house(studentData.getHouse())
        .bloodGroup(studentData.getBloodGroup())
        .dental(studentData.getDental())
        .eyesightL(studentData.getEyesightL())
        .eyesightR(studentData.getEyesightR())
        .build();
  }

  private GilcoHolisticReportModeldto.Term2 buildTerm2(Student student, String orgSlug) {
    var facilitatorStudentOpt = facilitatorStudentsRepository.findByStudentId(student.getId());
    var facilitator =
        facilitatorStudentOpt.isPresent() ? facilitatorStudentOpt.get().getFacilitator() : null;
    return GilcoHolisticReportModeldto.Term2.builder()
        .languageAndLiteracy(buildLanguageAndLiteracy(student, orgSlug, "t2"))
        .cognitiveDevelopment(buildCognitiveDevelopment(student, orgSlug, "t2"))
        .environMentalAwareness(buildEnvironMentalAwareness(student, orgSlug, "t2"))
        .physicalDevelopment(buildPhysicalDevelopment(student, orgSlug, "t2"))
        .socioEmotionalDevelopment(buildSocioEmotionalDevelopment(student, orgSlug, "t2"))
        .learningSkills(buildLearningSkills(student, orgSlug, "t2"))
        .remarks(facilitator != null ? facilitator.getClassFacilitatorRemarks() : null)
        .build();
  }

  private List<GilcoHolisticReportModeldto.SkillSubject> buildLearningSkills(
      Student student, String orgSlug, String termSlug) {

    var learningSkills =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(student, orgSlug, "learning-skills");

    Map<String, List<CompetenciesStudents>> groupedBySubject =
        learningSkills.stream()
            .collect(Collectors.groupingBy(cs -> cs.getPallaviPrePrimaryCompetencies().getSkill()));

    List<GilcoHolisticReportModeldto.SkillSubject> learningSkillsList = new ArrayList<>();

    groupedBySubject.forEach(
        (subject, competencyList) -> {
          competencyList.stream()
              .map(CompetenciesStudents::getPallaviPrePrimaryCompetencies)
              .forEach(
                  c ->
                      learningSkillsList.add(
                          GilcoHolisticReportModeldto.SkillSubject.builder()
                              .subject(c.getName())
                              .skillValue(
                                  getCompetencyForSkill(competencyList, c.getName(), termSlug))
                              .build()));
        });
    return learningSkillsList;
  }

  private List<GilcoHolisticReportModeldto.SkillSubject> buildSocioEmotionalDevelopment(
      Student student, String orgSlug, String termSlug) {

    var socioEmotionalDevelopment =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(
            student, orgSlug, "socio-emotional-development");

    Map<String, List<CompetenciesStudents>> groupedBySubject =
        socioEmotionalDevelopment.stream()
            .collect(Collectors.groupingBy(cs -> cs.getPallaviPrePrimaryCompetencies().getSkill()));

    List<GilcoHolisticReportModeldto.SkillSubject> socioEmotionalDevelopmentList =
        new ArrayList<>();

    groupedBySubject.forEach(
        (subject, competencyList) -> {
          competencyList.stream()
              .map(CompetenciesStudents::getPallaviPrePrimaryCompetencies)
              .forEach(
                  c ->
                      socioEmotionalDevelopmentList.add(
                          GilcoHolisticReportModeldto.SkillSubject.builder()
                              .subject(c.getName())
                              .skillValue(
                                  getCompetencyForSkill(competencyList, c.getName(), termSlug))
                              .build()));
        });
    return socioEmotionalDevelopmentList;
  }

  private GilcoHolisticReportModeldto.PhysicalDevelopment buildPhysicalDevelopment(
      Student student, String orgSlug, String termSlug) {

    var physicalDevelopmentData =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(student, orgSlug, "physical-development");

    Map<String, List<CompetenciesStudents>> groupedBySubject =
        physicalDevelopmentData.stream()
            .collect(Collectors.groupingBy(cs -> cs.getPallaviPrePrimaryCompetencies().getSkill()));

    List<GilcoHolisticReportModeldto.Table1> physicalSkillList = new ArrayList<>();
    List<GilcoHolisticReportModeldto.Table2> physicalDevelopmentList = new ArrayList<>();

    groupedBySubject.forEach(
        (subject, competencyList) -> {
          if ("Fine Motor Skills".equalsIgnoreCase(subject)) {
            physicalSkillList.add(
                GilcoHolisticReportModeldto.Table1.builder()
                    .subject(subject)
                    .creativity(getCompetencyForSkill(competencyList, "Creativity", termSlug))
                    .aestheticValue(
                        getCompetencyForSkill(competencyList, "Aesthetic Value", termSlug))
                    .pincerGrip(getCompetencyForSkill(competencyList, "Pincer Grip", termSlug))
                    .eyeHandCoordination(
                        getCompetencyForSkill(competencyList, "Eye Hand Coordination", termSlug))
                    .eyeFootCoordination(
                        getCompetencyForSkill(competencyList, "Eye Foot Coordination", termSlug))
                    .build());
          } else if ("Gross Motor Skills".equalsIgnoreCase(subject)) {
            physicalDevelopmentList.add(
                GilcoHolisticReportModeldto.Table2.builder()
                    .subject(subject)
                    .balanceOrCoordination(
                        getCompetencyForSkill(competencyList, "Balance/coordination", termSlug))
                    .locomotorSkills(
                        getCompetencyForSkill(competencyList, "Locomotor Skills", termSlug))
                    .manipulativeSkills(
                        getCompetencyForSkill(competencyList, "Manipulative Skills", termSlug))
                    .physicalStrength(
                        getCompetencyForSkill(competencyList, "Physical Strength", termSlug))
                    .heightAndWeight(
                        getCompetencyForSkill(competencyList, "Height/Weight", termSlug))
                    .build());
          }
        });

    return GilcoHolisticReportModeldto.PhysicalDevelopment.builder()
        .table1(physicalSkillList)
        .table2(physicalDevelopmentList)
        .build();
  }

  private List<GilcoHolisticReportModeldto.EnvironmentalAwareness> buildEnvironMentalAwareness(
      Student student, String orgSlug, String termSlug) {

    var environMentalAwarenessList =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(
            student, orgSlug, "environmental-awareness-development");

    Map<String, List<CompetenciesStudents>> groupedBySubject =
        environMentalAwarenessList.stream()
            .collect(Collectors.groupingBy(cs -> cs.getPallaviPrePrimaryCompetencies().getSkill()));

    List<GilcoHolisticReportModeldto.EnvironmentalAwareness> result = new ArrayList<>();

    groupedBySubject.forEach(
        (subject, competencyList) -> {
          result.add(
              GilcoHolisticReportModeldto.EnvironmentalAwareness.builder()
                  .themes(subject)
                  .knowledge(getCompetencyForSkill(competencyList, "Knowledge", termSlug))
                  .attitude(getCompetencyForSkill(competencyList, "Attitude", termSlug))
                  .application(getCompetencyForSkill(competencyList, "Application", termSlug))
                  .build());
        });

    return result;
  }

  private List<GilcoHolisticReportModeldto.CognitiveDevelopment> buildCognitiveDevelopment(
      Student student, String orgSlug, String termSlug) {

    var cognitiveDevelopmentList =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(student, orgSlug, "cognitive-development");

    Map<String, List<CompetenciesStudents>> groupedBySubject =
        cognitiveDevelopmentList.stream()
            .collect(Collectors.groupingBy(cs -> cs.getPallaviPrePrimaryCompetencies().getSkill()));

    List<GilcoHolisticReportModeldto.CognitiveDevelopment> result = new ArrayList<>();

    groupedBySubject.forEach(
        (subject, competencyList) -> {
          result.add(
              GilcoHolisticReportModeldto.CognitiveDevelopment.builder()
                  .mathematical(subject)
                  .knowledge(getCompetencyForSkill(competencyList, "Knowledge", termSlug))
                  .computingSkills(
                      getCompetencyForSkill(competencyList, "Computing Skills", termSlug))
                  .application(getCompetencyForSkill(competencyList, "Application", termSlug))
                  .build());
        });

    return result;
  }

  private GilcoHolisticReportModeldto.Term1 buildTerm1(Student student, String orgSlug) {
    var facilitatorStudentOpt = facilitatorStudentsRepository.findByStudentId(student.getId());
    var facilitator =
        facilitatorStudentOpt.isPresent() ? facilitatorStudentOpt.get().getFacilitator() : null;
    return GilcoHolisticReportModeldto.Term1.builder()
        .languageAndLiteracy(buildLanguageAndLiteracy(student, orgSlug, "t1"))
        .cognitiveDevelopment(buildCognitiveDevelopment(student, orgSlug, "t1"))
        .environMentalAwareness(buildEnvironMentalAwareness(student, orgSlug, "t1"))
        .physicalDevelopment(buildPhysicalDevelopment(student, orgSlug, "t1"))
        .socioEmotionalDevelopment(buildSocioEmotionalDevelopment(student, orgSlug, "t1"))
        .learningSkills(buildLearningSkills(student, orgSlug, "t1"))
        .remarks(facilitator != null ? facilitator.getClassFacilitatorRemarks() : null)
        .build();
  }

  private String getCompetencyForSkill(
      List<CompetenciesStudents> competencyList, String skill, String termSlug) {
    return competencyList.stream()
        .filter(cs -> cs.getPallaviPrePrimaryCompetencies().getName().equalsIgnoreCase(skill))
        .findFirst()
        .map(
            cs -> {
              CompetencyTypes competency =
                  "t1".equalsIgnoreCase(termSlug) ? cs.getTerm1() : cs.getTerm2();
              return competency != null ? competency.getValue() : null;
            })
        .orElse(null);
  }

  private List<GilcoHolisticReportModeldto.LanguageAndLiteracy> buildLanguageAndLiteracy(
      Student student, String orgSlug, String termSlug) {
    var competencies =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(
            student, orgSlug, "language-and-literacy-development");
    Map<String, List<CompetenciesStudents>> groupedBySubject =
        competencies.stream()
            .collect(Collectors.groupingBy(cs -> cs.getPallaviPrePrimaryCompetencies().getSkill()));
    List<GilcoHolisticReportModeldto.LanguageAndLiteracy> languageAndLiteracyList =
        new ArrayList<>();
    groupedBySubject.forEach(
        (subject, competencyList) -> {
          languageAndLiteracyList.add(
              GilcoHolisticReportModeldto.LanguageAndLiteracy.builder()
                  .subject(subject)
                  .listening(getCompetencyForSkill(competencyList, "Listening", termSlug))
                  .speaking(getCompetencyForSkill(competencyList, "Speaking", termSlug))
                  .reading(getCompetencyForSkill(competencyList, "Reading", termSlug))
                  .writing(getCompetencyForSkill(competencyList, "Writing", termSlug))
                  .vocabulary(getCompetencyForSkill(competencyList, "Vocabulary", termSlug))
                  .build());
        });

    return languageAndLiteracyList;
  }

  private List<CompetenciesStudents> getCompetenciesByStudentAndGradeAndOrgAndSubject(
      Student student, String orgSlug, String subjectSlug) {
    return competenciesStudentRepository.getCompetenciesByStudentAndGradeAndOrgAndSubject(
        student.getId(), student.getSection().getGradeSlug(), orgSlug, subjectSlug);
  }

  private GilcoHolisticReportModeldto.Header buildHolisticReportCardHeader(User user) {
    return GilcoHolisticReportModeldto.Header.builder()
        .imageUrl(
            Objects.isNull(user.getProfileImage())
                ? null
                : profileService.getProfileImageUrl(user.getProfileImage()))
        .build();
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("gillco-holistic-progress-report-card.xml");
  }
}
