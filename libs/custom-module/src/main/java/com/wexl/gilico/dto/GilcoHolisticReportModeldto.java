package com.wexl.gilico.dto;

import java.util.List;
import lombok.Builder;

public record GilcoHolisticReportModeldto() {

  @Builder
  public record Model(Header header, Body body) {}

  @Builder
  public record Header(String imageUrl) {}

  @Builder
  public record Body(
      String name,
      String className,
      String sectionName,
      String rollNo,
      String admissionNumber,
      String house,
      String dateOfBirth,
      Integer age,
      String address,
      String fatherName,
      String motherName,
      AllAboutMe allAboutMe,
      Term1 term1,
      Term2 term2) {}

  @Builder
  public record AllAboutMe(
      String familyPhoto,
      String studentPhoto,
      String liveIn,
      String favAnimals,
      String favColours,
      String favFoods,
      String favGames,
      String friends,
      String term1Height,
      String term1Weight,
      String term2Height,
      String term2Weight,
      String things,
      String aim,
      String favFlower,
      String favSports,
      String favSubject,
      String house,
      String bloodGroup,
      String dental,
      String eyesightL,
      String eyesightR) {}

  @Builder
  public record Term1(
      List<LanguageAndLiteracy> languageAndLiteracy,
      List<CognitiveDevelopment> cognitiveDevelopment,
      List<EnvironmentalAwareness> environMentalAwareness,
      PhysicalDevelopment physicalDevelopment,
      List<SkillSubject> socioEmotionalDevelopment,
      List<SkillSubject> learningSkills,
      String remarks,
      GradingSystem gradingSystem) {}

  @Builder
  public record Term2(
      List<LanguageAndLiteracy> languageAndLiteracy,
      List<CognitiveDevelopment> cognitiveDevelopment,
      List<EnvironmentalAwareness> environMentalAwareness,
      PhysicalDevelopment physicalDevelopment,
      List<SkillSubject> socioEmotionalDevelopment,
      List<SkillSubject> learningSkills,
      String remarks,
      GradingSystem gradingSystem) {}

  @Builder
  public record LanguageAndLiteracy(
      String subject,
      String listening,
      String speaking,
      String reading,
      String writing,
      String vocabulary) {}

  @Builder
  public record CognitiveDevelopment(
      String mathematical, String knowledge, String computingSkills, String application) {}

  @Builder
  public record EnvironmentalAwareness(
      String themes, String knowledge, String attitude, String application) {}

  @Builder
  public record SkillSubject(String subject, String skillValue) {}

  @Builder
  public record PhysicalDevelopment(List<Table1> table1, List<Table2> table2) {}

  @Builder
  public record Table1(
      String subject,
      String creativity,
      String aestheticValue,
      String pincerGrip,
      String eyeHandCoordination,
      String eyeFootCoordination) {}

  @Builder
  public record Table2(
      String subject,
      String balanceOrCoordination,
      String locomotorSkills,
      String manipulativeSkills,
      String physicalStrength,
      String heightAndWeight) {}

  @Builder
  public record GradingSystem(String title, List<GradeSystemDetail> gradeSystemDetails) {}

  @Builder
  public record GradeSystemDetail(String grade, String description) {}
}
