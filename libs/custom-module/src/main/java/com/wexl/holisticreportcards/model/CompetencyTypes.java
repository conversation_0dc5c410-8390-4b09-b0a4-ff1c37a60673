package com.wexl.holisticreportcards.model;

public enum CompetencyTypes {
  BEGINNER("beginner"),
  PROGRESSING("progressing"),
  PROFICIENT("proficient"),
  N_A("not applicable"),
  EXEMPLARY("exemplary"),
  SPECIAL_NEEDS("special_needs"),
  NEEDS_ENCOURAGEMENT("needs encouragement"),
  ADVANCED("advanced");

  private String value;

  CompetencyTypes(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }
}
