package com.wexl.retail.device.service;

import com.wexl.retail.device.dto.FirebaseTokenRequest;
import com.wexl.retail.device.dto.MobileAppUpdateStatus;
import com.wexl.retail.device.dto.UserDeviceInfoRequest;
import com.wexl.retail.device.model.Device;
import com.wexl.retail.device.repository.DeviceDataInfoResponse;
import com.wexl.retail.device.repository.DeviceRepository;
import com.wexl.retail.metrics.dto.DeviceInfoResponse;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DeviceService {
//  @Value("${mobileApp.updatableApps}")
//  private List<String> updatableSchoolApps;

  @Autowired private DeviceRepository deviceRepository;
  @Autowired private UserRepository userRepository;

  @Value("${app.mobile.minimumVersion}")
  private String minimumAppVersion;

  public void setMinimumAppVersion(String minimumAppVersion) {
    this.minimumAppVersion = minimumAppVersion;
  }

//  public void setUpdatableSchoolApps(List<String> list) {
//    this.updatableSchoolApps = list;
//  }

  public String getUserAppVersion(User user) {
    Optional<Device> optionalDevice = deviceRepository.findDeviceByUserDetailsId(user.getId());
    return optionalDevice.isEmpty() ? "" : optionalDevice.get().getAppVersion();
  }

  public void addUserDeviceInfo(UserDeviceInfoRequest userDeviceInfoRequest, String authUserId) {
    User user = userRepository.getUserByAuthUserId(authUserId);
    Device deviceInfo = user.getDeviceInfo();
    if (deviceInfo != null) {
      transformDeviceInfo(userDeviceInfoRequest, deviceInfo);
    } else {
      deviceInfo = new Device();
      transformDeviceInfo(userDeviceInfoRequest, deviceInfo);
      deviceInfo.setUserDetails(user);
      user.setDeviceInfo(deviceInfo);
    }
    userRepository.save(user);
  }

  private void transformDeviceInfo(UserDeviceInfoRequest userDeviceInfoRequest, Device deviceInfo) {
    deviceInfo.setDeviceModel(userDeviceInfoRequest.getDeviceModel());
    deviceInfo.setDeviceName(userDeviceInfoRequest.getDeviceName());
    deviceInfo.setDeviceMetadata(userDeviceInfoRequest.getDeviceMetadata());
    deviceInfo.setDeviceVersion(userDeviceInfoRequest.getDeviceVersion());
    deviceInfo.setAppName(userDeviceInfoRequest.getAppName());
    deviceInfo.setAppVersion(userDeviceInfoRequest.getAppVersion());
    deviceInfo.setAppBuildNumber(userDeviceInfoRequest.getAppBuildNumber());
    deviceInfo.setAppPackageName(userDeviceInfoRequest.getAppPackageName());
    deviceInfo.setGuid(userDeviceInfoRequest.getAppContext());
  }

  public void addUserFirebaseTokens(FirebaseTokenRequest firebaseTokenRequest, User user) {
    String firebaseToken = firebaseTokenRequest.getToken();
    userRepository.updateUserFirebaseTokens(firebaseToken, user.getAuthUserId());
  }

  public void writeAppInfoToCsv(String orgSlug, HttpServletResponse httpServletResponse) {
    List<DeviceDataInfoResponse> deviceInfo = deviceRepository.deviceInfoDetails(orgSlug);
    List<DeviceInfoResponse> deviceInfoResponses = userInfoResponseList(deviceInfo, orgSlug);
    csvFileForAppInfo(deviceInfoResponses, httpServletResponse);
  }

  private void csvFileForAppInfo(
      List<DeviceInfoResponse> deviceInfoResponses, HttpServletResponse httpServletResponse) {

    String[] csvHeader = {
      "Grade", "Section", "User Name", "Name", "Installed Mobile App?", "App Version"
    };

    List<List<String>> csvBody = new ArrayList<>();
    deviceInfoResponses.forEach(
        deviceInfoResponse ->
            csvBody.add(
                Arrays.asList(
                    deviceInfoResponse.getGrade(),
                    deviceInfoResponse.getSection(),
                    deviceInfoResponse.getUserName(),
                    deviceInfoResponse.getFullName(),
                    deviceInfoResponse.getInstalledMobileApp().toString(),
                    deviceInfoResponse.getAppVersion())));
    CsvUtils.generateCsv(csvHeader, csvBody, httpServletResponse);
  }

  private List<DeviceInfoResponse> userInfoResponseList(
      List<DeviceDataInfoResponse> userinfoAll, String orgSlug) {
    List<DeviceDataInfoResponse> deviceInfoDetails = deviceRepository.deviceInfoData(orgSlug);
    List<String> userNameList =
        deviceInfoDetails.stream().map(DeviceDataInfoResponse::getUserName).toList();

    return userinfoAll.stream()
        .map(
            m ->
                DeviceInfoResponse.builder()
                    .id(m.getId())
                    .userName(m.getUserName())
                    .section(m.getSection())
                    .grade(m.getGrade())
                    .appVersion(m.getAppVersion())
                    .fullName(m.getFirstName() + " " + m.getLastName())
                    .lastLogin(m.getLastLogin())
                    .organization(m.getOrganization())
                    .installedMobileApp(userNameList.contains(m.getUserName()))
                    .installedMobileApp(m.getAppVersion() == null ? Boolean.FALSE : Boolean.TRUE)
                    .userRole(m.getUserRole())
                    .build())
        .toList();
  }

  public MobileAppUpdateStatus isAppUpdateAvailable(
      UserDeviceInfoRequest request, String authUserId) {
    var dontUpdate = MobileAppUpdateStatus.builder().status(false).build();
    var update = MobileAppUpdateStatus.builder().status(true).build();
    if (request == null || request.getAppVersion() == null) {
      return dontUpdate;
    }
    String[] versions = request.getAppVersion().split("[.]");
    String[] minimumAppVersionsSplit = minimumAppVersion.split("[.]");

    if (versions.length != 3 || minimumAppVersionsSplit.length != 3) {
      return dontUpdate;
    }

    try {
      int value = Integer.parseInt(versions[2]);
      int configuredPatchVersionNumber = Integer.parseInt(minimumAppVersionsSplit[2]);
      if (isOldVersion(value)) {
        return dontUpdate;
      }
      if (value >= configuredPatchVersionNumber) {
        return dontUpdate;
      }

      if (isIPhone(request)) {
        return dontUpdate;
      }

//      String requestPackageName =
//          request.getAppPackageName() == null
//              ? getUserAppPackageName(authUserId)
//              : request.getAppPackageName();
//      if (updatableSchoolApps.contains(requestPackageName)) {
//        return update;
//      }

    } catch (Exception ex) {
      log.info(
          "Unable to parse MinAppVersion: {}, DeviceAppVersion{}",
          minimumAppVersion,
          request.getAppVersion());
    }

    return dontUpdate;
  }

//  private String getUserAppPackageName(String usename) {
//    return deviceRepository.getDeviceAppPackageNameByAuthUsername(usename).orElse(Strings.EMPTY);
//  }

  private boolean isIPhone(UserDeviceInfoRequest request) {
    return "iPhone".equals(request.getDeviceModel());
  }

  private boolean isOldVersion(int value) {
    // 1.0.24 - Ignore all the prior versions to 24.  Its a hack till we move out of this version
    return value < 24;
  }

  public void updateGuidWhileMobileNumberLogin(String appContext, User user) {
    Device deviceInfo = user.getDeviceInfo();
    if (deviceInfo != null) {
      deviceInfo.setGuid(appContext);
    } else {
      deviceInfo = new Device();
      deviceInfo.setGuid(appContext);
      deviceInfo.setUserDetails(user);
      user.setDeviceInfo(deviceInfo);
    }
    userRepository.save(user);
  }
}
