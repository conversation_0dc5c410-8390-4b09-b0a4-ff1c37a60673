package com.wexl.erp.appointments.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.erp.appointments.model.AppointmentStatus;
import com.wexl.erp.appointments.model.AppointmentType;
import com.wexl.retail.guardian.model.GuardianRole;
import java.util.List;
import lombok.Builder;

public class ParentAppointmentDto {

  public record Request(
      @JsonProperty("appointment_date") Long appointmentDate,
      @JsonProperty("appointment_reason") String appointmentReason,
      @JsonProperty("auth_id") List<String> authId,
      @JsonProperty("recipient_name") String recipientName,
      @JsonProperty("role") String role,
      @JsonProperty("type") AppointmentType type,
      @JsonProperty("guardian_role") GuardianRole relationType,
      @JsonProperty("mobile_number") Long mobileNumber,
      @JsonProperty("meeting_status") Boolean meetingStatus,
      @JsonProperty("visiting_status") Boolean visitingStatus,
      @JsonProperty("meet_person") String meetPerson) {}

  @Builder
  public record Response(
      Long appointmentId,
      String guardianName,
      String studentName,
      String studentSection,
      String gradeName,
      String gradeSlug,
      Long studentId,
      Long guardianId,
      Long appointmentDate,
      String appointmentReason,
      @JsonProperty("recipient_name") String recipientName,
      @JsonProperty("role") String role,
      AppointmentStatus status,
      Long appliedDate,
      String reviewedBy,
      Long reviewedOn,
      @JsonProperty("type") AppointmentType type,
      @JsonProperty("relation_type") GuardianRole relationType,
      @JsonProperty("mobile_number") Long mobileNumber,
      @JsonProperty("meeting_status") Boolean meetingStatus,
      @JsonProperty("visiting_status") Boolean visitingStatus,
      @JsonProperty("meet_person") String meetPerson) {}

  public record ApprovalRequest(@JsonProperty("status") AppointmentStatus status) {}
}
