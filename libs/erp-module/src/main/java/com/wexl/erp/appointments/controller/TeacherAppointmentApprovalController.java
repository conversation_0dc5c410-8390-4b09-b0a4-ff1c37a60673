package com.wexl.erp.appointments.controller;

import com.wexl.erp.appointments.dto.AppointmentSearchDto;
import com.wexl.erp.appointments.dto.GatePassDto;
import com.wexl.erp.appointments.dto.ParentAppointmentDto;
import com.wexl.erp.appointments.service.AppointmentService;
import com.wexl.erp.appointments.service.GatePassService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
public class TeacherAppointmentApprovalController {

  private final AppointmentService appointmentService;
  private final GatePassService gatePassService;

  @GetMapping("/orgs/{orgSlug}/teachers/{authUserId}/appointments")
  public List<ParentAppointmentDto.Response> getAppointmentRequests(
      @PathVariable String authUserId,
      @RequestParam(required = false) String gradeSlug,
      @RequestParam(required = false) String sectionName) {
    try {
      return appointmentService.getAllAppointmentRequestsSorted(authUserId, gradeSlug, sectionName);
    } catch (Exception e) {
      log.error("Error while fetching appointment requests: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping("/orgs/{orgSlug}/teachers/{authUserId}/appointments/search/{searchKey}")
  public List<ParentAppointmentDto.Response> searchAppointmentsByStudentName(
      @PathVariable String searchKey,
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody AppointmentSearchDto.Request request) {
    try {
      return appointmentService.getAppointmentRequestsByStudentName(
          searchKey, request, authUserId, orgSlug);
    } catch (Exception e) {
      log.error("Error while searching appointment requests: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/orgs/{orgSlug}/teachers/{authUserId}/appointments/review/{appointmentId}")
  public void approveOrRejectAppointment(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @PathVariable Long appointmentId,
      @RequestBody ParentAppointmentDto.ApprovalRequest request) {
    try {
      appointmentService.approveOrRejectAppointmentRequest(
          orgSlug, authUserId, appointmentId, request);
    } catch (Exception e) {
      log.error("Error while approving/rejecting appointment: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping("/orgs/{orgSlug}/students/{studentAuthId}/appointments")
  public AppointmentSearchDto.Response getAppointmentRequest(
      @PathVariable String orgSlug, @PathVariable String studentAuthId, @RequestParam String role) {
    return appointmentService.getTeachersOrStaff(orgSlug, studentAuthId, role);
  }

  @PostMapping("/orgs/{orgSlug}/teachers/{authUserId}/gatepass-review")
  public void approveOrRejectGatePass(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody GatePassDto.ApprovalRequest request) {
    try {
      gatePassService.approveOrRejectGatePassRequest(orgSlug, authUserId, request);
    } catch (Exception e) {
      log.error("Error while approving/rejecting gate pass: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping("/orgs/{orgSlug}/gatepass/pdf/{id}")
  public ResponseEntity<byte[]> getGatePassPdf(@PathVariable Long id) {
    byte[] pdfBytes = gatePassService.generateGatePassPdfBytes(id);

    return ResponseEntity.ok()
        .contentType(MediaType.APPLICATION_PDF)
        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=gatepass.pdf")
        .body(pdfBytes);
  }

  @PostMapping("orgs/{orgSlug}/teachers/{authUserId}/appointments/{appointmentId}")
  public void AppointmentMeetingStatus(
      @PathVariable Long appointmentId, @RequestBody ParentAppointmentDto.Request request) {
    appointmentService.AppointmentMeetingStatus(appointmentId, request);
  }
}
