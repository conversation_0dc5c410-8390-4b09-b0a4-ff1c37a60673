<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:th="http://www.thymeleaf.org">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin-top="18mm"  margin-left="5mm" />
        </fo:simple-page-master>
        <fo:simple-page-master master-name="page">
            <fo:region-body margin-top="18mm"  margin-left="5mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <!-- 1st Page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-10%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="278%" content-height="278%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="350%" height="350%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/gillco-background-image.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:table border="none" width="100%">
                    <fo:table-column column-width="100%" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="0.5cm" text-align="center">
                                    <fo:external-graphic
                                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/GILLCO_INTERNATIONAL_SCHOOL_LOGO.svg")'
                                            content-width="150px"
                                            content-height="auto"
                                            scaling="uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block font-size="22pt" font-weight="bold" text-align="center" space-before="3mm">
                    GILLCO INTERNATIONAL SCHOOL,
                    <fo:block>MOHALI</fo:block>
                </fo:block>

                <fo:block font-size="14pt"
                          text-align="center" color="#6ADA64" font-weight="bold" padding-top="1cm" space-after="3mm">
                    STUDENT PROFILE
                </fo:block>



                <fo:table margin-left="9mm">
                    <fo:table-column column-width="70mm"/>
                    <fo:table-column column-width="100%"/>
                    <fo:table-column column-width="0mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <!-- Empty left spacer -->
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>

                            <!-- Centered SVG Image -->
                            <fo:table-cell>
                                <fo:block text-align="center" padding-bottom="5mm">
                                    <fo:block-container width="34mm" height="38mm"
                                                        border="5pt solid #d5e6e8"
                                                        background-color="white"
                                                        display-align="center"
                                                        text-align="center"
                                                        margin-left="3mm"
                                                        margin-right="auto"
                                                        padding="1mm">
                                        <fo:block margin-right="5mm" font-size="10pt" font-family="Arial, sans-serif" text-align="center">
                                            <th:block th:if="${model.body.allAboutMe != null and model.body.allAboutMe.studentPhoto != null}">
                                                <fo:instream-foreign-object content-width="100%" content-height="100%">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                         xmlns:xlink="http://www.w3.org/1999/xlink"
                                                         width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                        <defs>
                                                            <filter id="brightnessFilter">
                                                                <feComponentTransfer>
                                                                    <feFuncR type="linear" slope="1"/>
                                                                    <feFuncG type="linear" slope="1"/>
                                                                    <feFuncB type="linear" slope="1"/>
                                                                </feComponentTransfer>
                                                            </filter>
                                                        </defs>
                                                        <image filter="url(#brightnessFilter)"
                                                               x="0" y="0" width="100%" height="100%"
                                                               th:xlink:href="@{${model.body.allAboutMe.studentPhoto}}"/>
                                                    </svg>
                                                </fo:instream-foreign-object>
                                            </th:block>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:block>
                            </fo:table-cell>


                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


                <fo:block-container border="1pt solid black"  background-color="#fdf2e9" padding="5mm"  margin-top="12pt"
                                    margin-bottom="12pt" display-align="center" margin-left="10mm" width="170mm">
                    <fo:block font-size="13pt" line-height="16pt">

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="50mm"/>
                            <fo:table-column column-width="115mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Name of the student :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.name}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>


                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="15mm"/>
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="18mm"/>
                            <fo:table-column column-width="38mm"/>
                            <fo:table-column column-width="20mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-body >
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Class</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.className}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Section</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.sectionName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Roll No.</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.rollNo}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>


                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="33mm"/>
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="16mm"/>
                            <fo:table-column column-width="28mm"/>
                            <fo:table-column column-width="12mm"/>
                            <fo:table-column column-width="41mm"/>
                            <fo:table-body >
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Admission No.</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.admissionNumber}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">House</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.house}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">DOB</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.dateOfBirth}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="20mm"/>
                            <fo:table-column column-width="145mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Address</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.address}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="33mm"/>
                            <fo:table-column column-width="132mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Father's Name</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.fatherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="130mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Mother's Name</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.motherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- PAGE 2 -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">
                <fo:block-container >
                    <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="30mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" >
                            <fo:table-column column-width="37mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>

                            <fo:table-header font-weight="bold"  >
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" height="7mm" text-align="center" number-columns-spanned="13"
                                                   background-color="#FFBF00">
                                        <fo:block padding-top="2.5mm">ATTENDANCE</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body border="1pt solid black">
                                <fo:table-row border="1pt solid black" height="7mm" text-align="center" font-weight="bold">
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >MONTHS</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >APR</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >MAY</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >JUN</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >JUL</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >AUG</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >SEP</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >OCT</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >NOV</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >DEC</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >JAN</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >FEB</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >MAR</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row border="1pt solid black" height="10mm" font-weight="bold" text-align="center"
                                              th:if="${model.body.attendance.workingDays != null}">
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.title}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.apr}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.may}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.jun}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.july}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.aug}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.sep}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.oct}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.nov}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.dec}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.jan}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.feb}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.mar}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row border="1pt solid black" height="8mm" font-weight="bold" text-align="center"
                                              th:if="${model.body.attendance.attendingDays != null}">
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.title}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.apr}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.may}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.jun}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.july}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.aug}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.sep}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.oct}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.nov}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.dec}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.jan}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.feb}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.mar}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row border="1pt solid black" height="10mm" font-weight="bold" text-align="center"
                                              th:if="${model.body.attendance.percentage != null}">
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.title}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.apr}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.may}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.jun}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.july}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.aug}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.sep}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.oct}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.nov}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.dec}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.jan}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.feb}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.percentage.mar}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>

                <fo:block-container>
                    <fo:block border-width="2mm" font-size="10pt" padding-left="30mm" space-before="3mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" border="1pt solid black" font-weight="bold">
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="21.2mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="21.2mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="41.2mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="29mm"/>
                            <fo:table-column column-width="9mm"/>
                            <fo:table-column column-width="2mm"/>

                            <fo:table-header font-weight="bold"  >
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" height="7mm" text-align="center" number-columns-spanned="11"
                                                   background-color="#FFBF00">
                                        <fo:block padding-top="2.5mm">INTERESTED (I am Interested in)</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body >
                                <fo:table-row  height="3mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  text-align="center">
                                    <fo:table-cell padding-top="4mm">
                                        <fo:block >Reading</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.reading ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="4mm">
                                        <fo:block >Dancing</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.dancing ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="4mm">
                                        <fo:block >Singing</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.singing ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Playing a Musical instrument</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.musicalInstrument ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="4mm">
                                        <fo:block >Sport or Games</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.sportsOrGames ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block/>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  text-align="center">
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Creative Writing</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.writing ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Gardening</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.gardening ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Yoga</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.yoga ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Art</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.art ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Craft</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.craft ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block/>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  text-align="center">
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Cooking</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.cooking ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Others</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.others ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm" number-columns-spanned="2">
                                        <fo:block >Please Specify</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  border-bottom="1pt solid black" number-columns-spanned="4">
                                        <fo:block th:text="${model.body.interests.specify}" text-align="left" padding-top="2mm"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="10" border-bottom="1pt solid black">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
                <fo:block-container >
                    <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="30mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" border="1pt solid black">
                            <fo:table-column column-width="26mm"/>
                            <fo:table-column column-width="41mm"/>
                            <fo:table-column column-width="27mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="25mm"/>
                            <fo:table-column column-width="41mm"/>

                            <fo:table-header font-weight="bold"  >
                                <fo:table-row>
                                    <fo:table-cell  height="7mm" text-align="center" number-columns-spanned="6"
                                                    background-color="#FFBF00">
                                        <fo:block padding-top="2.5mm">STUDENT'S MEDICAL PROFILE</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body border="1pt solid black" text-align="right">
                                <fo:table-row th:if="${model.body.medicalProfile == null}">
                                    <fo:table-cell number-columns-spanned="6">
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="10mm" th:if="${model.body.medicalProfile != null}">
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">Blood Group </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black" >
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.bloodGroup}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm" text-align="left">Height(im cms)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.height}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">Weight(in kg)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.weight}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="10mm" text-align="right">
                                    <fo:table-cell>
                                        <fo:block font-weight="bold" padding-top="8mm">Dental</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.dental}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">Eye Sight(R)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.rightEyeSight}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">(L)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.leftEyeSight}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="6">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
                <fo:block-container >
                    <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="30mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" border="1pt solid black">
                            <fo:table-column column-width="101mm"/>
                            <fo:table-column column-width="49mm"/>
                            <fo:table-column column-width="49mm"/>

                            <fo:table-header font-weight="bold"  border="1pt solid black">
                                <fo:table-row>
                                    <fo:table-cell  height="7mm" text-align="center" number-columns-spanned="3"
                                                    background-color="#FFBF00">
                                        <fo:block padding-top="2.5mm">SELF &amp; PEER ASSESSMENT</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="6mm">
                                    <fo:table-cell border="1pt solid black" number-rows-spanned="2">
                                        <fo:block font-weight="bold" padding-top="8mm" text-align="center">Life Skills</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block text-align="center">Self Assessment</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block font-weight="bold" text-align="center" >Peer Assessment</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="6mm">
                                    <fo:table-cell  border="1pt solid black" padding-top="4mm" text-align="center">
                                        <fo:block >Term 1</fo:block>
                                    </fo:table-cell>

                                    <fo:table-cell  border="1pt solid black" padding-top="4mm" text-align="center">
                                        <fo:block >Term 1</fo:block>
                                    </fo:table-cell>

                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body border="1pt solid black" text-align="left" >

                                <fo:table-row th:if="${model.body.selfAndPeerAssessments == null or #lists.isEmpty(model.body.selfAndPeerAssessments)}">
                                    <fo:table-cell number-columns-spanned="3" border="1pt solid black" padding="2mm">
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row
                                        height="7mm"
                                        th:each="data : ${model.body.selfAndPeerAssessments}"
                                        th:if="${data.skillName != null}">

                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block font-weight="bold" th:text="${data.skillName}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block text-align="center" th:text="${data.saTerm1}"> </fo:block>
                                    </fo:table-cell>

                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block text-align="center" th:text="${data.paTerm1}"> </fo:block>
                                    </fo:table-cell>

                                </fo:table-row>

                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm">
                    <fo:table-column column-width="40mm"/>
                    <fo:table-column column-width="125mm"/>
                    <fo:table-column column-width="40mm"/>
                    <fo:table-body>
                        <fo:table-row height="8mm" font-weight="bold">
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                            <fo:table-cell background-color="#F5F8AD" border="1pt solid black" padding-top="3mm" padding-left="5mm">
                                <fo:block color="#3AC19F">DESCRIPTORS: <fo:inline color="black"> [STRONGLY AGREE,AGREE,DISAGREE]</fo:inline></fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- 3rd PAGE -->
    <fo:page-sequence master-reference="page">
        <fo:flow flow-name="xsl-region-body">

            <!-- SCHOLASTIC AREAS TITLE -->
            <fo:block background-color="#FFBF00" text-align="center" font-weight="bold" font-size="12pt"
                      border="1pt solid black" padding="5pt" space-after="5pt">
                SCHOLASTIC AREAS:
            </fo:block>

            <!-- SCHOLASTIC AREAS TABLE -->
            <fo:table width="100%" font-size="8pt" table-layout="fixed" border-collapse="collapse">
                <fo:table-column column-width="15%"/>
                <!-- TERM I columns -->
                <fo:table-column column-width="5%"/>
                <fo:table-column column-width="5%"/>
                <fo:table-column column-width="5%"/>
                <fo:table-column column-width="5%"/>
                <fo:table-column column-width="5%"/>
                <fo:table-column column-width="5%"/>
                <fo:table-column column-width="5%"/>
                <fo:table-column column-width="5%"/>

                <fo:table-header>
                    <!-- Main header row -->
                    <fo:table-row>
                        <fo:table-cell border="1pt solid black" number-rows-spanned="3" display-align="center">
                            <fo:block font-weight="bold" text-align="center">SUBJECTS</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" number-columns-spanned="8" text-align="center">
                            <fo:block font-weight="bold">TERM I</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" number-columns-spanned="8" text-align="center">
                            <fo:block font-weight="bold">TERM II</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <!-- Second header row -->
                    <fo:table-row>
                        <fo:table-cell border="1pt solid black" number-columns-spanned="2" text-align="center">
                            <fo:block font-weight="bold">U.T. -I</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" number-columns-spanned="2" text-align="center">
                            <fo:block font-weight="bold">THEORY</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" number-columns-spanned="2" text-align="center">
                            <fo:block font-weight="bold">PROJECT/ PRACTICAL</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" number-columns-spanned="2" text-align="center">
                            <fo:block font-weight="bold">TOTAL</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <!-- Third header row -->
                    <fo:table-row>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MAX</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MARKS</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MAX</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MARKS</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MAX</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MARKS</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MAX</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center">
                            <fo:block font-weight="bold">MARKS</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-header>

                <fo:table-body>
                    <fo:table-row th:each="subject : ${model.body.scholasticAreas.subjects}" th:if="${subject != null}">
                        <fo:table-cell border="1pt solid black" padding="2pt">
                            <fo:block th:text="${subject.name}">ENGLISH CORE (301)</fo:block>
                        </fo:table-cell>
                        <!-- Term I -->
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1UtMax}">&#160;</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1UtMarks}">&#160;</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1TheoryMax}">&#160;</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1TheoryMarks}">&#160;</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1ProjectPracticalMax}">&#160;</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1ProjectPracticalMarks}">&#160;</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1TotalMax}">&#160;</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt">
                            <fo:block th:text="${subject.term1TotalMarks}">&#160;</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <!-- Fallback row if no subjects exist -->
                    <fo:table-row th:if="${model.body.scholasticAreas.subjects == null or model.body.scholasticAreas.subjects.isEmpty()}">
                        <fo:table-cell border="1pt solid black" padding="2pt">
                            <fo:block>No subjects available</fo:block>
                        </fo:table-cell>
                        <!-- Term I columns -->
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border="1pt solid black" text-align="center" padding="2pt"><fo:block>&#160;</fo:block></fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>

            <!-- CO-SCHOLASTIC AREAS TITLE -->
            <fo:block background-color="#FFBF00" text-align="center" font-weight="bold" font-size="12pt"
                      border="1pt solid black" padding="5pt" space-before="15pt" space-after="5pt">
                CO-SCHOLASTIC AREAS: [ON A 5 POINT (A-E) GRADING SCALE]
            </fo:block>

            <!-- CO-SCHOLASTIC AREAS TABLE -->
            <fo:table table-layout="fixed" font-size="10pt" width="100%" border-collapse="collapse">
                <fo:table-column column-width="70%"/>
                <fo:table-column column-width="30%"/>

                <fo:table-header>
                    <fo:table-row>
                        <fo:table-cell border="1pt solid black" padding="4pt" text-align="center">
                            <fo:block font-weight="bold">ACTIVITIES</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="4pt" text-align="center">
                            <fo:block font-weight="bold">GRADE</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-header>

                <fo:table-body>
                    <!-- Hierarchical co-scholastic structure -->
                    <th:block th:each="category : ${model.body.coScholasticCategories}" th:if="${category != null}">
                        <!-- Category header row -->
                        <fo:table-row>
                            <fo:table-cell border="1pt solid black" padding="4pt" background-color="#E6F3FF">
                                <fo:block font-weight="bold" th:text="${category.categoryName}">ACTIVITIES</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="4pt" text-align="center" background-color="#E6F3FF">
                                <fo:block font-weight="bold">GRADE</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <!-- Skills under each category -->
                        <fo:table-row th:each="skill : ${category.skills}" th:if="${skill != null}">
                            <fo:table-cell border="1pt solid black" padding="4pt" padding-left="15pt">
                                <fo:block th:text="${skill.skillName}">Work Experience</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="4pt" text-align="center">
                                <fo:block th:text="${skill.grade}">&#160;</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </th:block>

                    <!-- Fallback row if no co-scholastic categories exist -->
                    <fo:table-row th:if="${model.body.coScholasticCategories == null or model.body.coScholasticCategories.isEmpty()}">
                        <fo:table-cell border="1pt solid black" padding="4pt">
                            <fo:block>No co-scholastic areas available</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="4pt" text-align="center">
                            <fo:block>&#160;</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>

            <!-- REMARKS SECTION -->
            <fo:block font-size="10pt" font-weight="bold" space-before="20pt" text-align="center">REMARKS</fo:block>

            <fo:table width="100%" table-layout="fixed" border-collapse="collapse" space-before="6pt">
                <fo:table-column column-width="30%"/>
                <fo:table-column column-width="70%"/>
                <fo:table-body>
                    <fo:table-row>
                        <fo:table-cell border="1pt solid black">
                            <fo:block font-weight="bold" padding="4pt">Class Facilitator Remarks:</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black">
                            <fo:block padding="4pt" color="#B97E24" th:text="${model.body.classRemarks}"/>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row>
                        <fo:table-cell border="1pt solid black">
                            <fo:block font-weight="bold" padding="4pt">Principal's Remarks:</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black">
                            <fo:block padding="4pt" color="#46A96C" th:text="${model.body.principleRemarks}"/>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>

            <!-- SIGNATURE BLOCK -->
            <fo:table width="100%" space-before="15pt">
                <fo:table-column column-width="40%"/>
                <fo:table-column column-width="20%"/>
                <fo:table-column column-width="40%"/>
                <fo:table-body>
                    <fo:table-row>
                        <fo:table-cell border-bottom="1pt solid black" height="12mm"><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell border-bottom="1pt solid black" height="12mm"><fo:block>&#160;</fo:block></fo:table-cell>
                    </fo:table-row>
                    <fo:table-row>
                        <fo:table-cell text-align="center" font-weight="bold"><fo:block>Class Facilitator</fo:block></fo:table-cell>
                        <fo:table-cell><fo:block>&#160;</fo:block></fo:table-cell>
                        <fo:table-cell text-align="center" font-weight="bold"><fo:block>Principal</fo:block></fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>

        </fo:flow>
    </fo:page-sequence>

    <!-- 4th Page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-17mm" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="262%" content-height="262%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="327%" height="327%" viewBox="0 0 100 100">

                                <image  x="0" y="0" width="100%" height="100%" xlink:href="https://images-ext-1.discordapp.net/external/stPH7f10-GZmY8gaTQ0jSXI_sLu8cb_XWrO48vY4sQk/https/s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/Holistic%2520Progress%2520Report%25208th%2520page-1.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
